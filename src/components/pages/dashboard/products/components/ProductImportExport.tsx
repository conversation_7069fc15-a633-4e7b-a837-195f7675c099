"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  FileSpreadsheet,
  FileText,
  AlertCircle,
  CheckCircle,
  Info,
  Calendar,
  Settings,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import { createProductImportTemplate } from "@/utils/importTemplate";
import { importProducts } from "@/actions/import/products";
import {
  getProductReportDataWithFilters,
  getProductReportData,
} from "@/actions/reports/reports";

interface ImportSummary {
  productsCreated: number;
  categoriesCreated: number;
  unitsCreated: number;
  variantsCreated: number;
  errors: string[];
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  format: "excel" | "csv";
  includeSummary: boolean;
  includeCharts: boolean;
}

interface ProductImportExportProps {
  onRefresh?: () => void; // Callback to refresh the products list after import
}

// Function to create product-only Excel report
const createProductOnlyExcelReport = (
  products: any[],
  options: {
    reportTitle: string;
    includeSummary: boolean;
    totalProducts: number;
  }
) => {
  const workbook = XLSX.utils.book_new();

  // Create header info
  const headerData = [
    [options.reportTitle],
    [`Diekspor pada: ${new Date().toLocaleString("id-ID")}`],
    [`Total Produk: ${options.totalProducts}`],
    [], // Empty row
  ];

  // Define column headers
  const columnHeaders = [
    "ID Produk",
    "Nama Produk",
    "Deskripsi",
    "SKU",
    "Barcode",
    "Kategori Produk",
    "Unit",
    "Stok",
    "Harga Beli",
    "Harga Jual",
    "Harga Grosir",
    "Harga Diskon",
    "Tag Produk",
    "Varian Warna",
  ];

  // Prepare product data
  const productData = products.map((product) => {
    // Format tags as comma-separated string
    const tagsString =
      product.tags && product.tags.length > 0 ? product.tags.join(", ") : "-";

    // Format color variants as comma-separated string
    const variantsString =
      product.variants && product.variants.length > 0
        ? product.variants
            .map(
              (variant: any) => `${variant.colorName} (${variant.colorCode})`
            )
            .join(", ")
        : "-";

    return [
      product.id || "-",
      product.name || "-",
      product.description || "-",
      product.sku || "-",
      product.barcode || "-",
      product.category?.name || "Tidak ada kategori",
      product.unit || "pcs",
      product.stock || 0,
      product.cost || 0,
      product.price || 0,
      product.wholesalePrice || 0,
      product.discountPrice || 0,
      tagsString,
      variantsString,
    ];
  });

  // Combine all data
  const worksheetData = [...headerData, columnHeaders, ...productData];

  // Create worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths
  const columnWidths = [
    { wch: 25 }, // ID Produk
    { wch: 30 }, // Nama Produk
    { wch: 50 }, // Deskripsi (wider for longer text)
    { wch: 15 }, // SKU
    { wch: 15 }, // Barcode
    { wch: 20 }, // Kategori Produk
    { wch: 10 }, // Unit
    { wch: 10 }, // Stok
    { wch: 15 }, // Harga Beli
    { wch: 15 }, // Harga Jual
    { wch: 15 }, // Harga Grosir
    { wch: 15 }, // Harga Diskon
    { wch: 30 }, // Tag Produk (wider for multiple tags)
    { wch: 35 }, // Varian Warna (wider for multiple variants)
  ];
  worksheet["!cols"] = columnWidths;

  // Style the header rows
  const headerStyle = {
    font: { bold: true, sz: 14 },
    alignment: { horizontal: "center" },
    fill: { fgColor: { rgb: "E3F2FD" } },
  };

  const columnHeaderStyle = {
    font: { bold: true, sz: 12 },
    alignment: { horizontal: "center" },
    fill: { fgColor: { rgb: "BBDEFB" } },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  };

  // Apply styles to header
  if (worksheet["A1"]) worksheet["A1"].s = headerStyle;
  if (worksheet["A2"]) worksheet["A2"].s = { font: { sz: 10 } };
  if (worksheet["A3"]) worksheet["A3"].s = { font: { sz: 10 } };

  // Apply styles to column headers (row 5)
  const headerRowIndex = 5;
  columnHeaders.forEach((_, colIndex) => {
    const cellAddress = XLSX.utils.encode_cell({
      r: headerRowIndex - 1,
      c: colIndex,
    });
    if (worksheet[cellAddress]) {
      worksheet[cellAddress].s = columnHeaderStyle;
    }
  });

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Data Produk");

  return workbook;
};

export const ProductImportExport: React.FC<ProductImportExportProps> = ({
  onRefresh,
}) => {
  // Import states
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSummary, setImportSummary] = useState<ImportSummary | null>(
    null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export states
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
    format: "excel",
    includeSummary: false,
    includeCharts: false,
  });

  // Download template function
  const downloadTemplate = () => {
    try {
      const workbook = createProductImportTemplate();
      const fileName = `template-import-produk-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success("Template berhasil diunduh!");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Gagal mengunduh template");
    }
  };

  // Handle import function
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file maksimal 10MB");
      return;
    }

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/i)) {
      toast.error("Format file harus Excel (.xlsx atau .xls)");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportSummary(null);

    try {
      setImportProgress(20);

      const arrayBuffer = await file.arrayBuffer();
      setImportProgress(40);

      const result = await importProducts(arrayBuffer);
      setImportProgress(80);

      if (result.success) {
        setImportProgress(100);
        setImportSummary(result.summary || null);
        toast.success(result.success);

        // Refresh the products list if callback provided
        if (onRefresh) {
          setTimeout(() => {
            onRefresh();
          }, 1000); // Small delay to ensure data is updated
        }
      } else if (result.error) {
        setImportSummary(result.summary || null);
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Gagal mengimpor file");
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  // Handle advanced export function
  const handleAdvancedExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      setExportProgress(20);

      // Determine date range and period label
      let startDate: Date | undefined;
      let endDate: Date | undefined;
      let dateRange: string;
      let periodLabel: string;

      if (exportConfig.reportType === "harian") {
        startDate = new Date(exportConfig.selectedDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(exportConfig.selectedDate);
        endDate.setHours(23, 59, 59, 999);
        dateRange = "custom";
        periodLabel = `Harian - ${exportConfig.selectedDate.toLocaleDateString("id-ID")}`;
      } else if (exportConfig.reportType === "bulanan") {
        startDate = new Date(
          exportConfig.selectedYear,
          exportConfig.selectedMonth,
          1
        );
        endDate = new Date(
          exportConfig.selectedYear,
          exportConfig.selectedMonth + 1,
          0
        );
        endDate.setHours(23, 59, 59, 999);
        dateRange = "custom";
        const monthNames = [
          "Januari",
          "Februari",
          "Maret",
          "April",
          "Mei",
          "Juni",
          "Juli",
          "Agustus",
          "September",
          "Oktober",
          "November",
          "Desember",
        ];
        periodLabel = `Bulanan - ${monthNames[exportConfig.selectedMonth]} ${exportConfig.selectedYear}`;
      } else if (exportConfig.reportType === "tahunan") {
        startDate = new Date(exportConfig.selectedYear, 0, 1);
        endDate = new Date(exportConfig.selectedYear, 11, 31);
        endDate.setHours(23, 59, 59, 999);
        dateRange = "custom";
        periodLabel = `Tahunan - ${exportConfig.selectedYear}`;


      setExportProgress(40);

      // Fetch product data with date filtering
      const productResult = await getProductReportDataWithFilters({
        dateRange,
        startDate,
        endDate,
        category: undefined,
      });

      setExportProgress(70);

      if (productResult.error) {
        console.error("Product data fetch error:", productResult.error);
        throw new Error(productResult.error);
      }

      console.log(
        "Product data fetched:",
        productResult.data?.length || 0,
        "products"
      );
      console.log("Export config:", exportConfig);
      console.log("Date range:", { startDate, endDate, dateRange });

      // Check if we have data
      if (!productResult.data || productResult.data.length === 0) {
        toast.error(
          "Tidak ada data produk untuk diekspor pada periode yang dipilih"
        );
        return;
      }

      // Prepare report data structure
      const reportData = {
        reportType: "produk",
        products: productResult.data,
        summary: {
          period: periodLabel,
          generatedAt: new Date(),
          totalProducts: productResult.data?.length || 0,
        },
      };

      setExportProgress(85);

      if (exportConfig.format === "excel") {
        // Generate Excel export - Products only
        const workbook = createProductOnlyExcelReport(reportData.products, {
          reportTitle: `Data Produk - ${periodLabel}`,
          includeSummary: exportConfig.includeSummary,
          totalProducts: reportData.products.length,
        });

        setExportProgress(100);

        const fileName = `data-produk-${exportConfig.reportType}-${new Date().toISOString().split("T")[0]}.xlsx`;
        XLSX.writeFile(workbook, fileName);
      } else {
        // Generate CSV export - Products only
        let csvContent = `Data Produk - ${periodLabel}\n`;
        csvContent += `Diekspor pada: ${new Date().toLocaleString("id-ID")}\n`;
        csvContent += `Total Produk: ${reportData.products.length}\n\n`;

        // CSV Headers
        csvContent +=
          "ID Produk,Nama Produk,Deskripsi,SKU,Barcode,Kategori Produk,Unit,Stok,Harga Beli,Harga Jual,Harga Grosir,Harga Diskon,Tag Produk,Varian Warna\n";

        // CSV Data
        reportData.products.forEach((product: any) => {
          const categoryName = product.category?.name || "Tidak ada kategori";
          const description = (product.description || "").replace(/"/g, '""'); // Escape quotes
          const name = (product.name || "").replace(/"/g, '""'); // Escape quotes

          // Format tags as comma-separated string
          const tagsString =
            product.tags && product.tags.length > 0
              ? product.tags.join(", ")
              : "-";

          // Format color variants as comma-separated string
          const variantsString =
            product.variants && product.variants.length > 0
              ? product.variants
                  .map(
                    (variant: any) =>
                      `${variant.colorName} (${variant.colorCode})`
                  )
                  .join(", ")
              : "-";

          // Escape quotes in tags and variants
          const escapedTags = tagsString.replace(/"/g, '""');
          const escapedVariants = variantsString.replace(/"/g, '""');

          csvContent += `"${product.id || "-"}","${name}","${description}","${product.sku || "-"}","${product.barcode || "-"}","${categoryName}","${product.unit || "pcs"}",${product.stock || 0},${product.cost || 0},${product.price || 0},${product.wholesalePrice || 0},${product.discountPrice || 0},"${escapedTags}","${escapedVariants}"\n`;
        });

        setExportProgress(100);

        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute(
          "download",
          `data-produk-${exportConfig.reportType}-${new Date().toISOString().split("T")[0]}.csv`
        );
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      toast.success("Data produk berhasil diekspor!");
      setShowExportDialog(false);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor data produk");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Import Button and Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Produk
            </DialogTitle>
            <DialogDescription>
              Import data produk dari file Excel dengan mudah dan aman
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Cara Import Data Produk:
                  </h4>
                  <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1 list-decimal list-inside">
                    <li>
                      Download template Excel dengan klik tombol "Download
                      Template"
                    </li>
                    <li>Isi data produk sesuai format yang tersedia</li>
                    <li>Upload file Excel yang sudah diisi</li>
                    <li>Tunggu proses import selesai</li>
                  </ol>
                </div>
              </div>
            </div>

            {/* Download Template */}
            <div className="space-y-2">
              <h4 className="font-medium">1. Download Template</h4>
              <Button
                onClick={downloadTemplate}
                variant="outline"
                className="w-full flex items-center gap-2"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Download Template Excel
              </Button>
            </div>

            <Separator />

            {/* File Upload */}
            <div className="space-y-2">
              <h4 className="font-medium">2. Upload File Excel</h4>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
                <p className="text-sm text-slate-600 mb-2">
                  Klik untuk memilih file atau drag & drop
                </p>
                <p className="text-xs text-slate-500">
                  Format: .xlsx, .xls (Maksimal 10MB)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleImport}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4"
                  disabled={isImporting}
                >
                  {isImporting ? "Memproses..." : "Pilih File Excel"}
                </Button>
              </div>
            </div>

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Mengimpor data produk...</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} />
              </div>
            )}

            {/* Import Summary */}
            {importSummary && (
              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Hasil Import:
                </h4>
                <div className="text-sm space-y-1">
                  <p>
                    ✅ Produk berhasil dibuat: {importSummary.productsCreated}
                  </p>
                  <p>
                    📁 Kategori baru dibuat: {importSummary.categoriesCreated}
                  </p>
                  <p>📏 Satuan baru dibuat: {importSummary.unitsCreated}</p>
                  <p>🎨 Varian dibuat: {importSummary.variantsCreated}</p>

                  {importSummary.errors && importSummary.errors.length > 0 && (
                    <div className="mt-3">
                      <p className="text-red-600 font-medium flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        Error:
                      </p>
                      <div className="max-h-32 overflow-y-auto">
                        {importSummary.errors
                          .slice(0, 10)
                          .map((error: string, index: number) => (
                            <p key={index} className="text-xs text-red-600">
                              • {error}
                            </p>
                          ))}
                        {importSummary.errors.length > 10 && (
                          <p className="text-xs text-red-600">
                            ... dan {importSummary.errors.length - 10} error
                            lainnya
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Close Button */}
            {importSummary && (
              <div className="flex justify-end pt-4">
                <Button
                  onClick={() => {
                    setShowImportDialog(false);
                    setImportSummary(null);
                    if (fileInputRef.current) {
                      fileInputRef.current.value = "";
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  Tutup
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Advanced Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-3xl max-h-[80vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Export Data Produk
            </DialogTitle>
            <DialogDescription>
              Pilih periode dan format yang ingin diekspor untuk data produk
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Report Type Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Periode Export</Label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: "harian", label: "Harian", icon: Calendar },
                  { value: "bulanan", label: "Bulanan", icon: Calendar },
                  { value: "tahunan", label: "Tahunan", icon: Calendar },
                ].map((type) => (
                  <Card
                    key={type.value}
                    className={`cursor-pointer transition-all hover:shadow-sm ${
                      exportConfig.reportType === type.value
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "hover:bg-slate-50 dark:hover:bg-slate-800"
                    }`}
                    onClick={() =>
                      setExportConfig((prev) => ({
                        ...prev,
                        reportType: type.value as any,
                      }))
                    }
                  >
                    <CardContent className="p-3 text-center">
                      <type.icon className="h-5 w-5 mx-auto mb-1 text-blue-500" />
                      <p className="text-xs font-medium">{type.label}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <Separator />

            {/* Date/Period Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Pilih Periode</Label>

              {exportConfig.reportType === "harian" && (
                <div>
                  <DatePicker
                    date={exportConfig.selectedDate}
                    setDate={(date) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedDate: date || new Date(),
                      }));
                    }}
                    placeholder="Pilih tanggal"
                    className="w-full h-9"
                  />
                </div>
              )}

              {exportConfig.reportType === "bulanan" && (
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={exportConfig.selectedMonth?.toString() || ""}
                    onValueChange={(value) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedMonth: parseInt(value),
                      }));
                    }}
                  >
                    <SelectTrigger className="h-9">
                      <SelectValue placeholder="Bulan" />
                    </SelectTrigger>
                    <SelectContent>
                      {[
                        "Jan",
                        "Feb",
                        "Mar",
                        "Apr",
                        "Mei",
                        "Jun",
                        "Jul",
                        "Agu",
                        "Sep",
                        "Okt",
                        "Nov",
                        "Des",
                      ].map((month, index) => (
                        <SelectItem key={index} value={index.toString()}>
                          {month}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="h-9"
                  />
                </div>
              )}

              {exportConfig.reportType === "tahunan" && (
                <div>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="w-full h-9"
                  />
                </div>
              )}
            </div>

            <Separator />

            {/* Format Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Format Export</Label>
              <Select
                value={exportConfig.format}
                onValueChange={(value) =>
                  setExportConfig((prev) => ({
                    ...prev,
                    format: value as any,
                  }))
                }
              >
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">
                    <div className="flex items-center gap-2">
                      <FileSpreadsheet className="h-4 w-4" />
                      Excel (.xlsx)
                    </div>
                  </SelectItem>
                  <SelectItem value="csv">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      CSV (.csv)
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Separator />

            {/* Additional Options */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Opsi Tambahan</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeSummary"
                    checked={exportConfig.includeSummary}
                    onCheckedChange={(checked) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        includeSummary: checked as boolean,
                      }))
                    }
                  />
                  <Label
                    htmlFor="includeSummary"
                    className="text-sm cursor-pointer"
                  >
                    Sertakan ringkasan data
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeCharts"
                    checked={exportConfig.includeCharts}
                    onCheckedChange={(checked) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        includeCharts: checked as boolean,
                      }))
                    }
                    disabled
                  />
                  <Label
                    htmlFor="includeCharts"
                    className="text-sm cursor-pointer text-slate-500"
                  >
                    Sertakan grafik (Segera Hadir)
                  </Label>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons - Fixed at bottom */}
          <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
            <Button
              variant="outline"
              onClick={() => setShowExportDialog(false)}
            >
              Batal
            </Button>
            <Button
              onClick={handleAdvancedExport}
              disabled={isExporting}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {isExporting ? "Mengekspor..." : "Export Data Produk"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Progress Dialog */}
      {isExporting && (
        <Dialog open={isExporting}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Mengekspor Data Produk
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <span>Memproses data...</span>
                <span>{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} />
              <p className="text-sm text-slate-600">
                Mohon tunggu, sedang memproses dan mengunduh file data produk.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
